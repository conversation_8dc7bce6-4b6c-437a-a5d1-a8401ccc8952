# UCED Website

**Ullens Center for Educator Development**  
*Dedicated Teacher Training Unit of Ullens Education Foundation*

## 🎯 Overview

The UCED website is a modern, responsive, and accessible website for the Ullens Center for Educator Development. It showcases UCED's teacher training programs, partnerships, and community impact in progressive education across Nepal.

## ✨ Features

### 🔍 SEO Optimized
- Comprehensive meta tags for search engines
- Open Graph and Twitter Card support for social media
- JSON-LD structured data for rich snippets
- Semantic HTML5 structure
- Optimized page titles and descriptions

### 📱 Mobile-First Responsive Design
- Mobile-first CSS approach
- Responsive breakpoints for all device sizes
- Touch-friendly interface with 44px minimum touch targets
- Optimized typography scaling
- Mobile-specific navigation and layouts

### ♿ Accessibility Features
- WCAG 2.1 AA compliant
- Skip links for keyboard navigation
- ARIA labels and roles
- Proper heading hierarchy
- Screen reader support
- Keyboard navigation for all interactive elements
- Focus indicators with proper contrast
- Reduced motion support for users with vestibular disorders

### 🎨 Modern Design
- Clean, professional design
- Interactive slider with auto-advance
- Animated statistics counters
- Timeline layout for community impact
- Smooth animations and transitions
- Hover effects and visual feedback

### ⚡ Performance Optimized
- Optimized CSS and JavaScript
- Intersection Observer for performance
- Debounced scroll events
- Lazy loading considerations
- Minimal dependencies

## 📁 File Structure

```
uced-website/
├── index.html              # Main HTML file
├── css/
│   ├── main.css            # Main styles
│   └── responsive.css      # Responsive styles
├── js/
│   └── main.js            # JavaScript functionality
├── images/                 # Image assets
├── assets/                 # Additional assets
└── README.md              # This file
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser
- Web server (for local development)

### Installation
1. Clone or download the UCED website files
2. Open `index.html` in a web browser, or
3. Serve the files using a local web server:

```bash
# Using Python 3
python -m http.server 8000

# Using Node.js (with http-server)
npx http-server

# Using PHP
php -S localhost:8000
```

4. Navigate to `http://localhost:8000` in your browser

## 📋 Sections

### 1. Hero Section
- Eye-catching gradient background
- Clear value proposition
- Call-to-action button

### 2. About UCED
- Mission and overview
- Animated statistics
- Key information about accreditation

### 3. Training Programs
- Interactive slider with 4 main programs
- Auto-advancing with manual controls
- Additional programs grid

### 4. Community Impact
- Timeline layout (desktop)
- Mobile-friendly list (mobile)
- Four key impact areas

### 5. Academic Partnerships
- Kathmandu University collaboration
- Quality assurance information
- Academic credits details

### 6. Enrollment
- Different pathways for various audiences
- Call-to-action buttons
- Clear next steps

### 7. Contact Information
- Complete contact details
- Social media links
- Professional footer

## 🎛️ Customization

### Colors
The website uses CSS custom properties for easy color customization:

```css
:root {
    --primary-green: #4CAF50;
    --primary-blue: #2196F3;
    --primary-orange: #FF9800;
    --primary-purple: #9C27B0;
    /* ... other colors */
}
```

### Typography
The website uses the Inter font family. To change fonts, update the Google Fonts link in the HTML head and the CSS font-family declarations.

### Content
All content can be easily updated by editing the HTML file. The structure is semantic and well-organized for easy maintenance.

## 📱 Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Technical Details

### CSS Features Used
- CSS Grid and Flexbox for layouts
- CSS Custom Properties (variables)
- CSS Animations and Transitions
- Media queries for responsive design
- Modern CSS selectors

### JavaScript Features
- ES6+ syntax
- Intersection Observer API
- Event delegation
- Debounced functions
- Error handling

### Accessibility Features
- Semantic HTML5 elements
- ARIA attributes
- Keyboard navigation
- Focus management
- Screen reader support
- Skip links

## 📊 Performance

The website is optimized for performance with:
- Minimal HTTP requests
- Optimized CSS and JavaScript
- Efficient animations using CSS transforms
- Intersection Observer for scroll-based animations
- Debounced event handlers

## 🔒 Security

- No external dependencies that could pose security risks
- Proper use of `rel="noopener noreferrer"` for external links
- Content Security Policy ready

## 📞 Contact Information

**Ullens Center for Educator Development**
- **Address**: Khumaltar, Lalitpur-15, GPO Box. 8975, EPC 1477, Kathmandu, Nepal
- **Email**: <EMAIL>
- **Phone**: +977-1-5230944
- **Fax**: +977-1-5570365
- **Facebook**: https://www.facebook.com/uefuced

## 📄 License

© 2025 Ullens Center for Educator Development. All rights reserved.

## 🤝 Contributing

For updates or improvements to the website, please contact the UCED team through the official channels listed above.

---

*Built with modern web standards for optimal performance, accessibility, and user experience.*
