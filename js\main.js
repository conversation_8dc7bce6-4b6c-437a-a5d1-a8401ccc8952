// UCED Website - Main JavaScript

// Global variables
let slideIndex = 1;
let slideInterval;

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeSlider();
    initializeCounters();
    initializeMobileMenu();
    initializeScrollEffects();
    initializeSmoothScrolling();
    initializeKeyboardNavigation();
    initializeAccessibility();
});

// Slider Functionality
function initializeSlider() {
    startAutoSlide();
    
    // Pause auto-slide on hover and focus
    const slider = document.querySelector('.program-slider');
    if (slider) {
        slider.addEventListener('mouseenter', stopAutoSlide);
        slider.addEventListener('mouseleave', startAutoSlide);
        slider.addEventListener('focusin', stopAutoSlide);
        slider.addEventListener('focusout', startAutoSlide);
    }
}

function plusSlides(n) {
    showSlides(slideIndex += n);
}

function currentSlide(n) {
    showSlides(slideIndex = n);
}

function showSlides(n) {
    const slides = document.querySelector('.slider-container');
    const dots = document.querySelectorAll('.slider-dot');
    const totalSlides = 4;
    
    if (n > totalSlides) { slideIndex = 1; }
    if (n < 1) { slideIndex = totalSlides; }
    
    if (slides) {
        slides.style.transform = `translateX(-${(slideIndex - 1) * 100}%)`;
    }
    
    // Update dots and ARIA attributes
    dots.forEach((dot, index) => {
        dot.classList.remove('active');
        dot.removeAttribute('aria-current');
        if (index === slideIndex - 1) {
            dot.classList.add('active');
            dot.setAttribute('aria-current', 'true');
        }
    });

    // Announce slide change to screen readers
    const slider = document.querySelector('.program-slider');
    if (slider) {
        slider.setAttribute('aria-live', 'polite');
    }
}

function startAutoSlide() {
    slideInterval = setInterval(() => {
        slideIndex++;
        if (slideIndex > 4) slideIndex = 1;
        showSlides(slideIndex);
    }, 5000);
}

function stopAutoSlide() {
    clearInterval(slideInterval);
}

// Counter Animation
function initializeCounters() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.5
    });
    
    const aboutSection = document.getElementById('about');
    if (aboutSection) {
        observer.observe(aboutSection);
    }
}

function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;
        
        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current);
                setTimeout(updateCounter, 20);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    });
}

// Mobile Menu
function initializeMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Update ARIA attributes
            const isExpanded = navMenu.classList.contains('active');
            mobileToggle.setAttribute('aria-expanded', isExpanded);
            
            // Animate hamburger icon
            const spans = mobileToggle.querySelectorAll('span');
            spans.forEach((span, index) => {
                if (isExpanded) {
                    if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
                    if (index === 1) span.style.opacity = '0';
                    if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    span.style.transform = '';
                    span.style.opacity = '';
                }
            });
        });
        
        // Close menu when clicking on links
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                mobileToggle.setAttribute('aria-expanded', 'false');
                
                // Reset hamburger icon
                const spans = mobileToggle.querySelectorAll('span');
                spans.forEach(span => {
                    span.style.transform = '';
                    span.style.opacity = '';
                });
            });
        });
    }
}

// Scroll Effects
function initializeScrollEffects() {
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (header) {
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(10px)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'var(--background-light)';
                header.style.backdropFilter = 'none';
                header.style.boxShadow = 'var(--shadow-light)';
            }
        }
    });
}

// Smooth Scrolling
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Keyboard Navigation
function initializeKeyboardNavigation() {
    // Slider keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.target.closest('.program-slider')) {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                plusSlides(-1);
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                plusSlides(1);
            }
        }
    });
    
    // Skip link functionality
    const skipLink = document.querySelector('.skip-link');
    if (skipLink) {
        skipLink.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.focus();
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }
}

// Accessibility Enhancements
function initializeAccessibility() {
    // Add ARIA labels to dynamically generated content
    const sliderDots = document.querySelectorAll('.slider-dot');
    sliderDots.forEach((dot, index) => {
        if (!dot.getAttribute('aria-label')) {
            dot.setAttribute('aria-label', `Go to slide ${index + 1}`);
        }
    });
    
    // Ensure proper focus management
    const focusableElements = document.querySelectorAll(
        'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    );
    
    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid var(--primary-green)';
            this.style.outlineOffset = '2px';
        });
        
        element.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance Optimization
function initializePerformanceOptimizations() {
    // Lazy load images when they come into view
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
    
    // Optimize scroll events
    const optimizedScrollHandler = debounce(initializeScrollEffects, 10);
    window.addEventListener('scroll', optimizedScrollHandler);
}

// Error Handling
window.addEventListener('error', function(e) {
    console.error('UCED Website Error:', e.error);
    // Could send error reports to analytics service
});

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', initializePerformanceOptimizations);

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        plusSlides,
        currentSlide,
        showSlides,
        animateCounters
    };
}
