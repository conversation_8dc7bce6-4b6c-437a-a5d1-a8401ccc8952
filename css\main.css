/* UCED Website - Main Styles */

/* CSS Variables - Updated Color Scheme */
:root {
    --primary-color: #c73f4d;
    --secondary-color: #231f20;
    --accent-color: #00b9cd;
    --success-color: #00b9cd;
    --warning-color: #f9a51a;
    --danger-color: #c73f4d;
    --text-dark: #231f20;
    --text-medium: #4a4a4a;
    --text-light: #666666;
    --text-muted: #888888;
    --background-white: #ffffff;
    --background-light: #fafbfc;
    --background-gray: #f8f9fa;
    --background-section: #ffffff;
    --border-color: #e9ecef;
    --border-light: #f1f3f4;
    --shadow-subtle: 0 1px 3px rgba(35, 31, 32, 0.05);
    --shadow-light: 0 2px 8px rgba(35, 31, 32, 0.08);
    --shadow-medium: 0 4px 16px rgba(35, 31, 32, 0.1);
    --shadow-heavy: 0 8px 32px rgba(35, 31, 32, 0.12);
    --border-radius: 8px;
    --border-radius-large: 16px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--background-white);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: #333333;
}

.lead {
    font-size: 1.2rem;
    font-weight: 400;
    color: var(--text-dark);
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section {
    padding: 5rem 0;
    background-color: var(--background-section);
}

.section-alt {
    background-color: var(--background-light);
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    color: #231f20 !important;
    font-weight: 600;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color));
    border-radius: 2px;
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: var(--border-radius);
    z-index: 1000;
    transition: top 0.3s;
    font-weight: 500;
}

.skip-link:focus {
    top: 6px;
}

/* Header */
.header {
    background: var(--background-white);
    box-shadow: var(--shadow-light);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-light);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo h2 {
    color: var(--primary-color);
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.logo a {
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1rem;
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-medium);
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link:focus {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 80%;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--background-white) 0%, var(--background-light) 100%);
    color: var(--text-dark);
    min-height: 75vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 6rem 2rem 4rem;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(52, 152, 219, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(39, 174, 96, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-content h1 {
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.8s ease-out;
    font-weight: 700;
    color: var(--primary-color);
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: var(--text-medium);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    color: var(--text-light);
    animation: fadeInUp 0.8s ease-out 0.4s both;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    min-height: 44px;
    line-height: 1.2;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--accent-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-secondary:hover {
    background: #0099b3;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-accent {
    background: var(--success-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-accent:hover {
    background: #0099b3;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* About Section */
.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    margin-bottom: 4rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.8s ease-out;
}

.about-text p:nth-child(2) {
    animation-delay: 0.2s;
    animation-fill-mode: both;
}

.about-text p:nth-child(3) {
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.stat-card {
    text-align: center;
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius-large);
    transition: var(--transition);
    animation: slideInUp 0.8s ease-out;
    background: var(--background-white);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-subtle);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color));
}

.stat-card:nth-child(1) {
    animation-delay: 0.1s;
    animation-fill-mode: both;
}

.stat-card:nth-child(1)::before {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:nth-child(2) {
    animation-delay: 0.2s;
    animation-fill-mode: both;
}

.stat-card:nth-child(2)::before {
    background: linear-gradient(90deg, var(--accent-color), #0099b3);
}

.stat-card:nth-child(3) {
    animation-delay: 0.3s;
    animation-fill-mode: both;
}

.stat-card:nth-child(3)::before {
    background: linear-gradient(90deg, var(--success-color), #0099b3);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-medium);
    border-color: var(--accent-color);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.stat-card:nth-child(1) .stat-number {
    color: var(--primary-color);
}

.stat-card:nth-child(2) .stat-number {
    color: var(--accent-color);
}

.stat-card:nth-child(3) .stat-number {
    color: var(--success-color);
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Program Slider */
.program-slider {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-medium);
    margin-bottom: 4rem;
    border: 1px solid var(--border-color);
}

.slider-container {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.slide {
    min-width: 100%;
    padding: 4rem 3rem;
    text-align: center;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-white);
    position: relative;
}

.slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.03;
    pointer-events: none;
}

.slide-content {
    max-width: 600px;
    position: relative;
    z-index: 1;
}

.slide-green::before {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.slide-blue::before {
    background: linear-gradient(135deg, var(--accent-color), #0099b3);
}

.slide-orange::before {
    background: linear-gradient(135deg, var(--success-color), #0099b3);
}

.slide-purple::before {
    background: linear-gradient(135deg, var(--warning-color), #e6851a);
}

.slide h3 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: slideInDown 0.6s ease-out;
    color: var(--primary-color);
    font-weight: 700;
}

.slide-green h3 {
    color: var(--primary-color);
}

.slide-blue h3 {
    color: var(--accent-color);
}

.slide-orange h3 {
    color: var(--success-color);
}

.slide-purple h3 {
    color: var(--warning-color);
}

.slide-meta {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    color: var(--text-light);
    background: var(--background-light);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    display: inline-block;
}

.slide p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #333333;
    animation: fadeIn 0.8s ease-out 0.3s both;
}

/* Slider Navigation */
.slider-nav {
    text-align: center;
    margin-top: 2rem;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    background: var(--background-white);
    margin: 0 6px;
    cursor: pointer;
    transition: var(--transition);
    min-height: 44px;
    min-width: 44px;
    position: relative;
}

.slider-dot:hover {
    transform: scale(1.2);
    border-color: var(--accent-color);
}

.slider-dot.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.2);
}

.slider-prev,
.slider-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--background-white);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-medium);
    box-shadow: var(--shadow-light);
}

.slider-prev {
    left: 20px;
}

.slider-next {
    right: 20px;
}

.slider-prev:hover,
.slider-next:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-medium);
}

/* Additional Programs */
.additional-programs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.program-card {
    padding: 2.5rem;
    background: var(--background-white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    color: #333333;
}

.program-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color));
}

.program-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-medium);
    border-color: var(--accent-color);
}

.program-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.program-meta {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 1rem;
    background: var(--background-light);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    display: inline-block;
}

.program-card p {
    color: #333333 !important;
    line-height: 1.6;
    opacity: 1 !important;
    visibility: visible !important;
}

.program-card * {
    color: inherit;
}

.program-card h3 {
    color: var(--primary-color) !important;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.content-card {
    padding: 2.5rem;
    background: var(--background-white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.content-card:hover::before {
    transform: scaleX(1);
}

.content-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-medium);
    border-color: var(--accent-color);
}

.content-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.content-card p {
    color: #333333;
    line-height: 1.6;
}

.content-card .btn {
    margin-top: 1.5rem;
}

/* Timeline Styles */
.timeline-desktop {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    display: block;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, var(--accent-color), var(--success-color));
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-left {
    animation: slideInLeft 0.8s ease-out;
}

.timeline-right {
    animation: slideInRight 0.8s ease-out 0.2s both;
}

.timeline-left .timeline-content {
    flex: 1;
    text-align: right;
    padding-right: 2rem;
}

.timeline-right .timeline-content {
    flex: 1;
    text-align: left;
    padding-left: 2rem;
}

.timeline-right {
    flex-direction: row-reverse;
}

.timeline-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
}

.timeline-content p {
    color: #333333;
    line-height: 1.6;
}

.timeline-meta {
    font-weight: bold;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.timeline-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 4px solid white;
    z-index: 1;
    position: relative;
}

.timeline-dot-green {
    background: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(44, 62, 80, 0.2);
}

.timeline-dot-blue {
    background: var(--accent-color);
    box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.2);
}

.timeline-dot-orange {
    background: var(--success-color);
    box-shadow: 0 0 0 4px rgba(39, 174, 96, 0.2);
}

.timeline-dot-purple {
    background: var(--warning-color);
    box-shadow: 0 0 0 4px rgba(243, 156, 18, 0.2);
}

/* Mobile Timeline */
.timeline-mobile {
    display: none;
    position: relative;
    padding-left: 2rem;
}

.timeline-mobile::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, var(--accent-color), var(--success-color));
    border-radius: 2px;
}

.mobile-timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 1rem;
}

.mobile-timeline-item::before {
    content: '';
    position: absolute;
    left: -0.6rem;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--accent-color);
    border: 3px solid var(--background-white);
    box-shadow: 0 0 0 2px var(--accent-color);
}

.mobile-timeline-item h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
}

.mobile-timeline-meta {
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 1rem;
    background: var(--background-light);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    display: inline-block;
    font-size: 0.9rem;
}

.mobile-timeline-item p {
    color: #333333;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--background-white);
    color: var(--text-dark);
    padding: 4rem 0 2rem;
    border-top: 1px solid var(--border-color);
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-weight: 600;
    font-size: 1.2rem;
}

.footer-section p {
    color: var(--text-medium);
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.footer-section a {
    color: var(--text-medium);
    text-decoration: none;
    display: block;
    margin-bottom: 0.75rem;
    transition: var(--transition);
    padding: 0.25rem 0;
}

.footer-section a:hover {
    color: var(--accent-color);
    padding-left: 0.5rem;
}

.social-links {
    margin-top: 1.5rem;
}

.social-links a {
    display: inline-block;
    margin-right: 1rem;
    color: var(--accent-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
    text-align: center;
}

.footer-bottom p {
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.btn:focus,
.slider-prev:focus,
.slider-next:focus,
.slider-dot:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.2);
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Card Text Visibility Fix - High Priority */
.additional-programs .program-card p,
.program-card p,
.content-card p,
.content-grid .content-card p,
.slide p,
.slide-content p {
    color: #333333 !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
}

/* Ensure all card containers have proper text color inheritance */
.program-card,
.content-card,
.slide,
.slide-content {
    color: #333333 !important;
}

/* Make sure headings in cards are visible */
.program-card h3,
.content-card h3,
.slide h3 {
    color: var(--primary-color) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Comprehensive text visibility fix for all card elements */
.program-card *,
.content-card *,
.slide *,
.slide-content * {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Override any potential white text */
.section p,
.section-alt p,
div p {
    color: #333333 !important;
}

/* Emergency text visibility fix - very high priority */
p {
    color: #333333 !important;
    text-shadow: none !important;
    background: transparent !important;
}

/* Specific fixes for problematic sections */
#programs p,
#partnerships p,
#enrollment p,
#contact p,
#community p {
    color: #333333 !important;
    opacity: 1 !important;
    visibility: visible !important;
}
